'use client'

import React from 'react';
import {
  X, Mouse, Layers, Wand2, Upload, Type,
} from 'lucide-react';

interface HelpModalProps {
  onClose: () => void;
}

export const HelpModal = ({ onClose }: HelpModalProps) => {
  const shortcuts = [
    {
      key: 'Delete',
      description: 'Delete selected objects',
    },
    {
      key: 'Ctrl/Cmd + Z',
      description: 'Undo last action',
    },
    {
      key: 'Ctrl/Cmd + Y',
      description: 'Redo last action',
    },
    {
      key: 'Alt + Drag',
      description: 'Pan around the canvas',
    },
    {
      key: 'Double Click',
      description: 'Edit text inline',
    },
    {
      key: 'Escape',
      description: 'Deselect all objects',
    },
  ];

  const features = [
    {
      icon: <Wand2 size={20} />,
      title: 'Generate Images',
      description: 'Create AI-generated images with custom prompts and styles',
    },
    {
      icon: <Upload size={20} />,
      title: 'Upload Images',
      description: 'Import your own images and photos to the canvas',
    },
    {
      icon: <Type size={20} />,
      title: 'Add Text',
      description: 'Insert and customize text elements with various fonts',
    },
    {
      icon: <Layers size={20} />,
      title: 'Layer Management',
      description: 'Organize, reorder, and control visibility of canvas elements',
    },
    {
      icon: <Mouse size={20} />,
      title: 'Object Controls',
      description: 'Resize, rotate, and position objects with precision',
    },
  ];

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center bg-black/50 backdrop-blur-sm">
      <div className="bg-neutral-800 rounded-lg shadow-xl max-w-2xl w-full mx-4 max-h-[90vh] overflow-y-auto border border-neutral-600">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-neutral-600">
          <h2 className="text-xl font-semibold text-gray-200">Canvas Editor Help</h2>
          <button
            onClick={onClose}
            className="p-2 rounded-lg hover:bg-neutral-700 transition-colors"
          >
            <X size={20} className="text-gray-400" />
          </button>
        </div>

        {/* Content */}
        <div className="p-6 space-y-8">
          {/* Features Section */}
          <div>
            <h3 className="text-lg font-medium text-gray-200 mb-4">Features</h3>
            <div className="grid gap-4">
              {features.map((feature, index) => (
                <div key={index} className="flex items-start gap-3 p-3 rounded-lg bg-neutral-700/50">
                  <div className="text-violets-are-blue mt-0.5">
                    {feature.icon}
                  </div>
                  <div>
                    <h4 className="font-medium text-gray-200">{feature.title}</h4>
                    <p className="text-sm text-gray-400 mt-1">{feature.description}</p>
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Keyboard Shortcuts */}
          <div>
            <h3 className="text-lg font-medium text-gray-200 mb-4">Keyboard Shortcuts</h3>
            <div className="space-y-3">
              {shortcuts.map((shortcut, index) => (
                <div key={index} className="flex items-center justify-between p-3 rounded-lg bg-neutral-700/50">
                  <span className="text-sm text-gray-400">{shortcut.description}</span>
                  <kbd className="px-2 py-1 text-xs font-mono bg-neutral-600 border border-neutral-500 rounded text-gray-300">
                    {shortcut.key}
                  </kbd>
                </div>
              ))}
            </div>
          </div>

          {/* Tips Section */}
          <div>
            <h3 className="text-lg font-medium text-gray-200 mb-4">Tips & Tricks</h3>
            <div className="space-y-3 text-sm text-gray-400">
              <div className="p-3 rounded-lg bg-violets-are-blue/10 border border-violets-are-blue/30">
                <p><strong className="text-gray-300">Selection:</strong> Click on objects to select them. Hold Shift to select multiple objects.</p>
              </div>
              <div className="p-3 rounded-lg bg-green-900/20 border border-green-700/30">
                <p><strong className="text-gray-300">Positioning:</strong> Use the corner handles to resize objects while maintaining aspect ratio.</p>
              </div>
              <div className="p-3 rounded-lg bg-yellow-900/20 border border-yellow-700/30">
                <p><strong className="text-gray-300">Text Editing:</strong> Double-click any text to edit it directly on the canvas.</p>
              </div>
              <div className="p-3 rounded-lg bg-purple-900/20 border border-purple-700/30">
                <p><strong className="text-gray-300">Layers:</strong> Use the layers panel to organize your design and control object stacking order.</p>
              </div>
              <div className="p-3 rounded-lg bg-pink-900/20 border border-pink-700/30">
                <p><strong className="text-gray-300">Zoom:</strong> Click the zoom percentage to fit the canvas to your view automatically.</p>
              </div>
            </div>
          </div>

          {/* Canvas Controls */}
          <div>
            <h3 className="text-lg font-medium text-gray-200 mb-4">Canvas Controls</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
              <div className="space-y-2">
                <h4 className="font-medium text-gray-200">Mouse Controls</h4>
                <ul className="space-y-1 text-gray-400">
                  <li>• Click: Select object</li>
                  <li>• Drag: Move object</li>
                  <li>• Alt + Drag: Pan canvas</li>
                  <li>• Double-click: Edit text</li>
                </ul>
              </div>
              <div className="space-y-2">
                <h4 className="font-medium text-gray-200">Object Manipulation</h4>
                <ul className="space-y-1 text-gray-400">
                  <li>• Corner handles: Resize</li>
                  <li>• Rotation handle: Rotate</li>
                  <li>• Center: Move position</li>
                  <li>• Right-click: Context menu</li>
                </ul>
              </div>
            </div>
          </div>
        </div>

        {/* Footer */}
        <div className="p-6 border-t border-neutral-600 bg-neutral-700/50 rounded-b-lg">
          <p className="text-sm text-gray-400 text-center">
            Need more help? Check out our documentation or contact support.
          </p>
        </div>
      </div>
    </div>
  );
};
